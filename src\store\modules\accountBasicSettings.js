import axios from 'axios'

export default {
  namespaced: true,
  state: {
    basicSettings: {}
  },
  mutations: {
    setBasicSettings (state, { accountId, data }) {
      state.basicSettings[accountId] = data
    }
  },
  actions: {
    async getAccountBasicSettings ({ state, commit }, accountId) {
      if (state.basicSettings[accountId]) {
        return state.basicSettings[accountId]
      }

      const result = await axios.get(`/api/accounts/${accountId}/basicsettings`)

      commit('setBasicSettings', {
        accountId: accountId,
        data: result.data
      })

      return result.data
    },
    async updateAccountBasicSettings ({ state, commit }, { accountId, data }) {
      await axios.post(`/api/accounts/${accountId}/basicsettings`, data)
      // Clear cache to force refresh on next get
      state.basicSettings = {}
    }
  }
}
