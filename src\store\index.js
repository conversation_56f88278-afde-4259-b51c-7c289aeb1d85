import Vue from 'vue'
import Vuex from 'vuex'

import authenticationModule from './modules/authentication'
import usersModule from './modules/users'
import siteSettingsModule from './modules/siteSettings'
import vehicleDetails from './modules/vehicledetails'
import categoryData from './modules/categorydata'
import editSectionController from './modules/editSectionController'
import analyticsGa4Vuex from './modules/analytics_ga4'
import craigslistModule from './modules/craigslist'
import accountSettings from './modules/accountSettings'
import accountBasicSettings from './modules/accountBasicSettings'
import accountManagement from './modules/accountManagement'
import groupsVuex from './modules/groups'
import systemTools from './modules/systemTools'
import craigslistPost from './modules/craigslistPost'
import craigslistSettings from './modules/craigslistSettings'
import craigslistServiceSettings from './modules/craigslistServiceSettings'
import inventoryAlertSettings from './modules/inventoryAlertSettings'
import leadsModule from './modules/leads'
import leadsCampaignTypes from './modules/leadsCampaignTypes'
import leadsAccountSettings from './modules/leadsAccountSettings'
import website from './modules/website'
import inventoryCategoryData from './modules/inventoryCategoryData'
import specials from './modules/specials'
import eBay from './modules/eBay'
import eBayRevise from '@/store/modules/eBayRevise'
import eBayScheduling from '@/store/modules/eBayScheduling'
import siteBoxManager from './modules/siteBoxManager'
import userAnnouncements from './modules/userAnnouncements'
import userInventoryGeneralData from './modules/userInventoryGeneralData'

Vue.use(Vuex)

export default new Vuex.Store({
  state: {
  },
  modules: {
    authentication: authenticationModule,
    users: usersModule,
    siteSettings: siteSettingsModule,
    craigslist: craigslistModule,
    accountSettings: accountSettings,
    accountBasicSettings: accountBasicSettings,
    accountManagement: accountManagement,
    details: vehicleDetails,
    categoryData: categoryData,
    editSectionController: editSectionController,
    analyticsGa4: analyticsGa4Vuex,
    groups: groupsVuex,
    systemTools: systemTools,
    craigslistPost: craigslistPost,
    craigslistSettings: craigslistSettings,
    craigslistServiceSettings: craigslistServiceSettings,
    inventoryAlertSettings: inventoryAlertSettings,
    leads: leadsModule,
    leadsCampaignTypes: leadsCampaignTypes,
    leadsAccountSettings: leadsAccountSettings,
    website: website,
    inventoryCategoryData: inventoryCategoryData,
    specials: specials,
    eBay: eBay,
    eBayRevise: eBayRevise,
    eBayScheduling: eBayScheduling,
    siteBoxManager: siteBoxManager,
    userAnnouncements: userAnnouncements,
    userInventoryGeneralData: userInventoryGeneralData
  }
})
