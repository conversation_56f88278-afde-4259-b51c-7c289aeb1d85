<template>
  <div v-if="readyToShow" class="account-listing">
    <account-listing
      title="Account Listing"
      :filters="getFilters"
      :listing="listing"
      @searchChange="applySearch"
      @pageChange="pageChanged"
      @pageSizeChange="changePageSize"
      @sortChange="sortChange"
      :hasActions="hasActions && user.isEbizAdmin"
    >
      <div class="media align-items-center" slot="accountId" slot-scope="data">
        <listing-link :account-id="data.data.accountId" :force-link-name="routeName" :query="query">{{data.data.accountId}}</listing-link>
      </div>

      <div class="media align-items-center" slot="accountName" slot-scope="data">
        <listing-link :account-id="data.data.accountId" :force-link-name="routeName" :query="query">{{data.data.accountName}}</listing-link>
      </div>

      <div slot="status" slot-scope="data">
        {{getAccountStatusLabel(data.data.accountStatus)}}
      </div>

      <div slot="websiteStatus" slot-scope="data">
        <span v-if="data.data.accountWebsiteStatus === 1">Process Disabling</span>
        <span v-else-if="data.data.accountWebsiteStatus === 2">Disabled</span>
        <span v-else-if="data.data.accountWebsiteStatus === 3">Process Enabling</span>
        <span v-else-if="data.data.accountWebsiteStatus === 4">Enabled</span>
        <span v-else>-</span>
      </div>

      <div slot="mfaStatus" slot-scope="data">
        <span v-if="data.data.isMfaEnabled">Enabled</span>
        <span v-else>Disabled</span>
      </div>

      <div slot="websiteUrl" slot-scope="data">
        <a v-if="data.data.defaultWebsiteUrl" :href="data.data.defaultWebsiteUrl" target="_blank">{{data.data.defaultWebsiteUrl}}</a>
        <span v-else>-</span>
      </div>

      <div slot="actions" slot-scope="data">
        <template v-if="isAccountActive(data.data.accountStatus) || isAccountOnHold(data.data.accountStatus)">
          <div class="d-flex flex-column">
            <c-button v-if="isAccountActive(data.data.accountStatus)"
              :loading="data.data.isLoading"
              :message="`Are you sure you want to move account ${data.data.accountId} to OnHold?`"
              variant="secondary"
              className="mb-1 w-100"
              size="sm"
              @confirm="moveAccountToOnHold(data.data)">
              Move to OnHold
            </c-button>
            <c-button v-else
              :loading="data.data.isLoading"
              :message="`Are you sure you want to move account ${data.data.accountId} to Active?`"
              variant="secondary"
              className="mb-1 w-100"
              size="sm"
              @confirm="moveAccountToActive(data.data)">
              Move to Active
            </c-button>
            <c-button :loading="data.data.isLoading"
              :message="`Are you sure you want to move account ${data.data.accountId} to Pending? You will not be able to revert this!`"
              variant="primary"
              className="w-100"
              size="sm"
              @confirm="moveAccountToPending(data.data)">
              Move to Pending
            </c-button>
          </div>
        </template>
        <c-button v-else-if="isAccountPending(data.data.accountStatus)" className="w-100" :loading="data.data.isLoading" :message="`Are you sure you want to Close account ${data.data.accountId}? You will not be able to revert this!`" variant="primary" size="sm" @confirm="closeAccount(data.data)">Close</c-button>
      </div>

    </account-listing>
  </div>
</template>

<script>
import {ObjectSchema} from '@/shared/common/objectHelpers'
import QueryStringHelper from '../../../shared/common/queryStringHelper'
import paging from '@/components/_shared/paging.vue'
import accountListingRaw from '../accountListingRaw'
import accountStatuses from '@/shared/accounts/accountStatuses'
import AppsListingLink from './AppsListingLink'
import {mapGetters} from 'vuex'

const defaultValues = new ObjectSchema({
  page: { type: Number, default: 1 },
  pageSize: { type: Number, default: 25 },
  sort: { type: Number, default: 0 },
  search: { type: String, default: '' }
})

let queryHelper = new QueryStringHelper(defaultValues)

export default {
  name: 'AccountListing',
  props: ['routeName', 'applicationType', 'query', 'hasActions'],
  components: {
    'paging': paging,
    'account-listing': accountListingRaw,
    'listing-link': AppsListingLink
  },
  data () {
    return {
      filters: defaultValues.getObject(),
      listing: null,
      count: 0,
      readyToShow: false,
      showModal: false
    }
  },
  mounted () {
    this.filters = queryHelper.parseQueryStringToObject(this.$router)
    this.populateAccountListing()
      .then(x => { this.readyToShow = true })
  },
  computed: {
    ...mapGetters('users', ['userInfo']),
    user () {
      return (this.userInfo || {}).user || {}
    },
    getFilters () {
      return {
        ...this.filters,
        count: this.count
      }
    }
  },
  methods: {
    populateAccountListing () {
      return this.$store.dispatch(
        'accountManagement/getAccountListing',
        {
          applicationType: this.applicationType,
          filters: this.filters
        })
        .then(x => {
          let result = x.data

          this.filters.page = result.pageNumber
          this.filters.pageSize = result.pageSize
          this.filters.sort = result.sortType
          this.filters.search = result.search
          this.listing = result.accountsSummaries
          // this.listing = this.listing.map(x => ({...x, IsPASEnabled: this.listing.indexOf(x) % 5}))
          this.count = result.accountsCount

          queryHelper.rebuildParamsInQueryString(this.$router, this.filters)
        })
        .catch(ex => this.$logger.handleError(ex, 'Can\'t get account listing', {filters: this.filters}))
    },
    applySearch (search) {
      this.filters.page = 1
      this.filters.search = search
      this.populateAccountListing()
    },
    pageChanged (newPage) {
      this.filters.page = newPage
      this.populateAccountListing()
    },
    changePageSize (newSize) {
      this.filters.pageSize = newSize
      this.filters.page = 1
      this.populateAccountListing()
    },
    sortChange (newSort) {
      this.filters.sort = newSort
      this.populateAccountListing()
    },
    getAccountStatusLabel (accountStatus) {
      switch (accountStatus) {
        case accountStatuses.Active.Value:
          return accountStatuses.Active.Label
        case accountStatuses.Pending.Value:
          return accountStatuses.Pending.Label
        case accountStatuses.OnHold.Value:
          return accountStatuses.OnHold.Label
        case accountStatuses.Closed.Value:
          return accountStatuses.Closed.Label
        default:
          return accountStatuses.Undefined.Label
      }
    },
    isAccountActive (accountStatus) {
      return accountStatus === accountStatuses.Active.Value
    },
    isAccountOnHold (accountStatus) {
      return accountStatus === accountStatuses.OnHold.Value
    },
    isAccountPending (accountStatus) {
      return accountStatus === accountStatuses.Pending.Value
    },
    moveAccountToPending (accountItem) {
      accountItem.isLoading = true
      this.$store.dispatch('accountManagement/moveAccountToPending', {accountId: accountItem.accountId})
        .then(x => {
          accountItem.accountStatus = accountStatuses.Pending.Value
          this.$toaster.success(`Account ${accountItem.accountId} is moved to Pending.`, { timeout: 8000 })
        })
        .catch(ex => {
          this.$toaster.error(`Failed to move account ${accountItem.accountId} to Pending. Try again later.`, { timeout: 8000 })
          this.$logger.handleError(ex, `Failed to move account ${accountItem.accountId} to Pending`)
        })
        .finally(function () {
          accountItem.isLoading = false
        })
    },
    moveAccountToOnHold (accountItem) {
      accountItem.isLoading = true
      this.$store.dispatch('accountManagement/moveAccountToOnHold', {accountId: accountItem.accountId})
        .then(x => {
          accountItem.accountStatus = accountStatuses.OnHold.Value
          this.$toaster.success(`Account ${accountItem.accountId} is moved to OnHold.`, { timeout: 8000 })
        })
        .catch(ex => {
          this.$toaster.error(`Failed to move account ${accountItem.accountId} to OnHold. Try again later.`, { timeout: 8000 })
          this.$logger.handleError(ex, `Failed to move account ${accountItem.accountId} to OnHold`)
        })
        .finally(function () {
          accountItem.isLoading = false
        })
    },
    moveAccountToActive (accountItem) {
      accountItem.isLoading = true
      this.$store.dispatch('accountManagement/moveAccountToActive', {accountId: accountItem.accountId})
        .then(x => {
          accountItem.accountStatus = accountStatuses.Active.Value
          this.$toaster.success(`Account ${accountItem.accountId} is moved to Active.`, { timeout: 8000 })
        })
        .catch(ex => {
          this.$toaster.error(`Failed to move account ${accountItem.accountId} to Active. Try again later.`, { timeout: 8000 })
          this.$logger.handleError(ex, `Failed to move account ${accountItem.accountId} to Active`)
        })
        .finally(function () {
          accountItem.isLoading = false
        })
    },
    closeAccount (accountItem) {
      accountItem.isLoading = true
      this.$store.dispatch('accountManagement/closeAccount', {accountId: accountItem.accountId})
        .then(x => {
          accountItem.accountStatus = accountStatuses.Closed.Value
          this.$toaster.success(`Account ${accountItem.accountId} is Closed.`, { timeout: 8000 })
        })
        .catch(ex => {
          this.$toaster.error(`Failed to close account ${accountItem.accountId}. Try again later.`, { timeout: 8000 })
          this.$logger.handleError(ex, `Failed to close account ${accountItem.accountId}.`)
        })
        .finally(function () {
          accountItem.isLoading = false
        })
    }
  }
}
</script>
