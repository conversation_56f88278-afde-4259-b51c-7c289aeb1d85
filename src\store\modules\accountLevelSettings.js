import axios from 'axios'

export default {
  namespaced: true,
  state: {
    accountSettings: {}
  },
  mutations: {
    setAccountSettings (state, { accountId, data }) {
      state.accountSettings[accountId] = data
    }
  },
  actions: {
    async getAccountSettings ({ state, commit }, accountId) {
      if (state.accountSettings[accountId]) {
        return state.accountSettings[accountId]
      }

      const result = await axios.get(`/api/accounts/${accountId}/basicsettings`)

      commit('setAccountSettings', {
        accountId: accountId,
        data: result.data
      })

      return result.data
    },
    async updateAccountSettings ({ state, commit }, { accountId, data }) {
      await axios.post(`/api/accounts/${accountId}/basicsettings`, data)

      // Clear cache to force refresh on next get
      state.accountSettings = {}
    }
  }
}
