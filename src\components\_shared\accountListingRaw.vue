<template>
  <div class="account-listing">
    <h4 v-if="title">{{title}}</h4>

    <b-form v-on:submit.prevent="applySearch">
      <b-card no-body>
        <b-card-body>
          <div class="form-row">
            <b-col :xl="searchFullWidth ? '12' : '6'">
              <b-input-group>
                <b-form-input ref="search" v-model="localFilters.search" placeholder="Search..."></b-form-input>
                <b-input-group-append>
                  <b-btn type="submit">Go</b-btn>
                </b-input-group-append>
              </b-input-group>
            </b-col>
          </div>
        </b-card-body>
      </b-card>
    </b-form>

    <b-card class="table-holder overflow-auto">
      <b-table
        striped
        hover
        :sort-by="tableSortBy"
        :sort-desc="tableSortDesc"
        @sort-changed="onSortChanged"
        :fields="fields"
        :items="listing"
        responsive>
        <template #cell(accountId)="data">
          <slot name="accountId" :data="data.item">

          </slot>
        </template>
        <template #cell(accountName)="data">
          <slot name="accountName" :data="data.item">

          </slot>
        </template>

        <template #cell(status)="data">
          <slot name="status" :data="data.item">

          </slot>
        </template>

        <template #cell(websiteStatus)="data">
          <slot name="websiteStatus" :data="data.item">

          </slot>
        </template>

        <template #cell(mfaStatus)="data">
          <slot name="mfaStatus" :data="data.item">

          </slot>
        </template>

        <template #cell(websiteUrl)="data">
          <slot name="websiteUrl" :data="data.item">

          </slot>
        </template>

        <template #cell(actions)="data">
          <slot name="actions" :data="data.item">

          </slot>
        </template>
      </b-table>
      <!-- Pagination -->
      <paging v-if="!noPaging"
              class="pt-0 pb-3"
              :pageNumber="localFilters.page"
              :pageSize="localFilters.pageSize"
              :totalItems="localFilters.count"
              titled
              pageSizeSelector
              @numberChanged="pageChanged"
              @changePageSize="changePageSize"/>
      <!-- / Pagination -->
    </b-card>
  </div>

</template>

<script>
import paging from '@/components/_shared/paging.vue'
import constants from '@/shared/accounts/constants'

export default {
  name: 'AccountListingRaw',
  props: {
    title: {
      type: String,
      default: 'Account Listing'
    },
    filters: {
      type: Object,
      required: true
    },
    listing: {
      type: Array,
      required: true
    },
    noPaging: {
      type: Boolean,
      default: false
    },
    searchFullWidth: {
      type: Boolean,
      default: false
    },
    hasActions: {
      type: Boolean,
      default: false
    }
  },
  components: {
    'paging': paging
  },
  data () {
    return {
      localFilters: this.filters
    }
  },
  computed: {
    fields () {
      let toReturn = [{
        key: 'accountId',
        label: 'Account Id',
        sortable: true,
        sortTypeAsc: constants.accountSortTypes.accountIdAsc,
        sortTypeDesc: constants.accountSortTypes.accountIdDesc,
        tdClass: 'py-2 align-middle',
        thStyle: 'width: 175px;'
      }, {
        key: 'accountName',
        label: 'Account Name',
        sortable: true,
        sortTypeAsc: constants.accountSortTypes.accountNameAsc,
        sortTypeDesc: constants.accountSortTypes.accountNameDesc,
        tdClass: 'py-2 align-middle'
      }]

      if (this.hasActions) {
        toReturn.push({
          key: 'status',
          label: 'Status',
          sortable: false,
          tdClass: 'py-2 align-middle',
          thStyle: 'width: 175px;'
        })
        toReturn.push({
          key: 'websiteStatus',
          label: 'Website Status',
          sortable: false,
          tdClass: 'py-2 align-middle',
          thStyle: 'width: 175px;'
        })
        toReturn.push({
          key: 'mfaStatus',
          label: 'MFA Status',
          sortable: false,
          tdClass: 'py-2 align-middle',
          thStyle: 'width: 175px;'
        })
        toReturn.push({
          key: 'websiteUrl',
          label: 'Website URL',
          sortable: false,
          tdClass: 'py-2 align-middle',
          thStyle: 'width: 350px;'
        })
        toReturn.push({
          key: 'actions',
          label: 'Actions',
          sortable: false,
          tdClass: 'py-2 align-middle',
          thStyle: 'width: 175px;'
        })
      }
      return toReturn
    },
    tableSortBy () {
      const sortedColumn = this.fields.find(x => x.sortTypeAsc === this.localFilters.sort || x.sortTypeDesc === this.localFilters.sort)
      if (sortedColumn) {
        return sortedColumn.key
      } else {
        return null
      }
    },
    tableSortDesc () {
      const sortedColumn = this.fields.find(x => x.sortTypeAsc === this.localFilters.sort || x.sortTypeDesc === this.localFilters.sort)
      if (sortedColumn) {
        return sortedColumn.sortTypeDesc === this.localFilters.sort
      } else {
        return false
      }
    }
  },
  methods: {
    applySearch () {
      this.localFilters.page = 1
      this.$emit('searchChange', this.localFilters.search)
    },
    onSortChanged (value) {
      if (value !== undefined && value.sortBy !== '') {
        const sortingColumn = this.fields.find(x => x.key === value.sortBy)
        if (value.sortDesc) {
          this.$emit('sortChange', sortingColumn.sortTypeDesc)
        } else {
          this.$emit('sortChange', sortingColumn.sortTypeAsc)
        }
      }
    },
    pageChanged (newPage) {
      this.localFilters.page = newPage
      this.$emit('pageChange', newPage)
    },
    changePageSize (newSize) {
      this.localFilters.pageSize = newSize
      this.localFilters.page = 1
      this.$emit('pageSizeChange', newSize)
    }
  },
  watch: {
    'localFilters.search' (val) {
      this.$emit('searchChangeLive', val)
    },
    'filters': {
      deep: true,
      handler (newVal) {
        this.localFilters = newVal
      }
    }
  }
}
</script>
