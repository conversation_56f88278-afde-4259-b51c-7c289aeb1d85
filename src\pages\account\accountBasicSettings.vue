<template>
  <div>
    <div class="d-flex justify-content-between align-items-center mb-3">
      <h4>Account Basic Settings</h4>
      <b-button variant="outline-secondary" @click="goBack">
        <i class="ion ion-md-arrow-back mr-1"></i>
        Back to Accounts
      </b-button>
    </div>

    <div v-if="isLoading" class="text-center">
      <b-spinner variant="primary"></b-spinner>
      <p class="mt-2">Loading account settings...</p>
    </div>

    <div v-else-if="isError" class="text-center">
      <b-alert variant="danger" show>
        <h5>Error Loading Settings</h5>
        <p>Unable to load account basic settings. Please try again later.</p>
        <b-button variant="outline-danger" @click="loadSettings">
          <i class="ion ion-md-refresh mr-1"></i>
          Retry
        </b-button>
      </b-alert>
    </div>

    <div v-else>
      <b-card>
        <b-card-header>
          <h5 class="mb-0">Basic Account Information</h5>
        </b-card-header>
        <b-card-body>
          <b-row>
            <b-col md="6">
              <b-form-group label="Account ID" label-for="accountId">
                <b-form-input
                  id="accountId"
                  v-model="settings.accountId"
                  readonly
                  variant="outline-secondary"
                />
              </b-form-group>
            </b-col>
            <b-col md="6">
              <b-form-group label="Account Name" label-for="accountName">
                <b-form-input
                  id="accountName"
                  v-model="settings.accountName"
                  :readonly="!isEditing"
                />
              </b-form-group>
            </b-col>
          </b-row>

          <b-row>
            <b-col md="6">
              <b-form-group label="Account Status" label-for="accountStatus">
                <b-form-select
                  id="accountStatus"
                  v-model="settings.accountStatus"
                  :options="accountStatusOptions"
                  :disabled="!isEditing"
                />
              </b-form-group>
            </b-col>
            <b-col md="6">
              <b-form-group label="Website Status" label-for="websiteStatus">
                <b-form-select
                  id="websiteStatus"
                  v-model="settings.accountWebsiteStatus"
                  :options="websiteStatusOptions"
                  :disabled="!isEditing"
                />
              </b-form-group>
            </b-col>
          </b-row>

          <b-row>
            <b-col md="6">
              <b-form-group label="MFA Status" label-for="mfaStatus">
                <b-form-checkbox
                  id="mfaStatus"
                  v-model="settings.isMfaEnabled"
                  :disabled="!isEditing"
                >
                  Multi-Factor Authentication Enabled
                </b-form-checkbox>
              </b-form-group>
            </b-col>
            <b-col md="6">
              <b-form-group label="Default Website URL" label-for="websiteUrl">
                <b-form-input
                  id="websiteUrl"
                  v-model="settings.defaultWebsiteUrl"
                  :readonly="!isEditing"
                  placeholder="https://example.com"
                />
              </b-form-group>
            </b-col>
          </b-row>
        </b-card-body>
        
        <b-card-footer class="d-flex justify-content-end">
          <b-button
            v-if="!isEditing"
            variant="primary"
            @click="startEditing"
          >
            <i class="ion ion-md-create mr-1"></i>
            Edit Settings
          </b-button>
          
          <div v-else>
            <b-button
              variant="outline-secondary"
              class="mr-2"
              @click="cancelEditing"
            >
              Cancel
            </b-button>
            <b-button
              variant="primary"
              :disabled="isSaving"
              @click="saveSettings"
            >
              <b-spinner v-if="isSaving" small class="mr-1"></b-spinner>
              <i v-else class="ion ion-md-checkmark mr-1"></i>
              Save Changes
            </b-button>
          </div>
        </b-card-footer>
      </b-card>
    </div>
  </div>
</template>

<script>
import { mapGetters } from 'vuex'
import accountStatuses from '@/shared/accounts/accountStatuses'

export default {
  name: 'AccountBasicSettings',
  props: {
    accountId: {
      type: Number,
      required: true
    }
  },
  data () {
    return {
      isLoading: true,
      isError: false,
      isEditing: false,
      isSaving: false,
      settings: {},
      originalSettings: {}
    }
  },
  computed: {
    ...mapGetters('users', ['userInfo']),
    user () {
      return (this.userInfo || {}).user || {}
    },
    accountStatusOptions () {
      return [
        { value: accountStatuses.Active.Value, text: accountStatuses.Active.Label },
        { value: accountStatuses.Pending.Value, text: accountStatuses.Pending.Label },
        { value: accountStatuses.OnHold.Value, text: accountStatuses.OnHold.Label },
        { value: accountStatuses.Closed.Value, text: accountStatuses.Closed.Label }
      ]
    },
    websiteStatusOptions () {
      return [
        { value: 1, text: 'Process Disabling' },
        { value: 2, text: 'Disabled' },
        { value: 3, text: 'Process Enabling' },
        { value: 4, text: 'Enabled' }
      ]
    }
  },
  async mounted () {
    await this.loadSettings()
  },
  methods: {
    async loadSettings () {
      this.isLoading = true
      this.isError = false

      try {
        const data = await this.$store.dispatch('accountBasicSettings/getAccountBasicSettings', this.accountId)
        this.settings = { ...data }
        this.originalSettings = { ...data }
      } catch (error) {
        this.isError = true
        this.$logger.handleError(error, `Failed to load basic settings for account ${this.accountId}`)
        this.$toaster.error('Failed to load account settings. Please try again.')
      } finally {
        this.isLoading = false
      }
    },
    startEditing () {
      this.isEditing = true
      this.originalSettings = { ...this.settings }
    },
    cancelEditing () {
      this.isEditing = false
      this.settings = { ...this.originalSettings }
    },
    async saveSettings () {
      this.isSaving = true

      try {
        await this.$store.dispatch('accountBasicSettings/updateAccountBasicSettings', {
          accountId: this.accountId,
          data: this.settings
        })

        this.isEditing = false
        this.originalSettings = { ...this.settings }
        this.$toaster.success('Account settings updated successfully!')
      } catch (error) {
        this.$logger.handleError(error, `Failed to update basic settings for account ${this.accountId}`)
        this.$toaster.error('Failed to update account settings. Please try again.')
      } finally {
        this.isSaving = false
      }
    },
    goBack () {
      this.$router.push({ name: 'account-listing' })
    }
  }
}
</script>

<style scoped>
.card-header h5 {
  color: #495057;
}
</style>
