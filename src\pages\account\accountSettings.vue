<template>
  <div v-if="!isLoading">
    <div class="d-flex align-items-center justify-content-between">
      <h4 class="d-flex justify-content-start">
        Account Settings
      </h4>
    </div>

    <div v-if="isError" class="text-center mb-3">
      <b-alert variant="danger" show>
        <h5>Error Loading Settings</h5>
        <p>Unable to load account settings. Please try again later.</p>
        <b-button variant="outline-danger" @click="loadSettings">
          <i class="ion ion-md-refresh mr-1"></i>
          Retry
        </b-button>
      </b-alert>
    </div>

    <b-card v-else>
      <b-row class="border-bottom pb-1">
        <b-col class="align-middle">
          <span>
            <b>MFA Settings</b>
          </span>
        </b-col>
        <b-col class="text-right">
          <b-button
            v-if="!isEditing"
            variant="secondary"
            size="sm"
            @click="startEditing"
          >
            <i class="ion ion-md-create mr-1"></i>
            Edit
          </b-button>

          <div v-else>
            <b-button
              variant="primary"
              size="sm"
              :disabled="isSaving"
              @click="saveSettings"
            >
              <b-spinner v-if="isSaving" small class="mr-1"></b-spinner>
              <font-awesome-icon v-else icon="cloud-upload-alt" />
              <span class="btn-title">Save</span>
            </b-button>
            <b-button
              variant="secondary"
              class="ml-2"
              size="sm"
              @click="cancelEditing"
            >
              Cancel
            </b-button>
          </div>
        </b-col>
      </b-row>

      <div class="mt-3">
        <b-row>
          <b-col md="6">
            <b-form-group label="Account ID" label-for="accountId">
              <b-form-input
                id="accountId"
                v-model="settings.accountId"
                readonly
                variant="outline-secondary"
              />
            </b-form-group>
          </b-col>
          <b-col md="6">
            <b-form-group label="Account Name" label-for="accountName">
              <b-form-input
                id="accountName"
                v-model="settings.accountName"
                readonly
                variant="outline-secondary"
              />
            </b-form-group>
          </b-col>
        </b-row>

        <b-row>
          <b-col md="6">
            <b-form-group label="Account Status" label-for="accountStatus">
              <b-form-select
                id="accountStatus"
                v-model="settings.accountStatus"
                :options="accountStatusOptions"
                disabled
              />
            </b-form-group>
          </b-col>
          <b-col md="6">
            <b-form-group label="Website Status" label-for="websiteStatus">
              <b-form-select
                id="websiteStatus"
                v-model="settings.accountWebsiteStatus"
                :options="websiteStatusOptions"
                disabled
              />
            </b-form-group>
          </b-col>
        </b-row>

        <b-row>
          <b-col md="6">
            <b-form-group label="MFA Status" label-for="mfaStatus">
              <b-form-checkbox
                id="mfaStatus"
                v-model="settings.isMfaEnabled"
                :disabled="!isEditing"
              >
                Multi-Factor Authentication Enabled
              </b-form-checkbox>
            </b-form-group>
          </b-col>
        </b-row>
      </div>
    </b-card>
  </div>
  <div v-else class="my-5 text-center">
    <b-spinner variant="primary"></b-spinner>
    <p class="mt-2">Loading account settings...</p>
  </div>
</template>

<script>
import { mapGetters } from 'vuex'
import accountStatuses from '@/shared/accounts/accountStatuses'

export default {
  name: 'AccountSettings',
  metaInfo: {
    title: 'Account Settings'
  },
  props: {
    accountId: {
      type: Number,
      required: true
    }
  },
  data () {
    return {
      isLoading: true,
      isError: false,
      isEditing: false,
      isSaving: false,
      settings: {},
      originalSettings: {}
    }
  },
  computed: {
    ...mapGetters('users', ['userInfo']),
    user () {
      return (this.userInfo || {}).user || {}
    },
    accountStatusOptions () {
      return [
        { value: accountStatuses.Active.Value, text: accountStatuses.Active.Label },
        { value: accountStatuses.Pending.Value, text: accountStatuses.Pending.Label },
        { value: accountStatuses.OnHold.Value, text: accountStatuses.OnHold.Label },
        { value: accountStatuses.Closed.Value, text: accountStatuses.Closed.Label }
      ]
    },
    websiteStatusOptions () {
      return [
        { value: 1, text: 'Process Disabling' },
        { value: 2, text: 'Disabled' },
        { value: 3, text: 'Process Enabling' },
        { value: 4, text: 'Enabled' }
      ]
    }
  },
  async mounted () {
    await this.loadSettings()
  },
  methods: {
    async loadSettings () {
      this.isLoading = true
      this.isError = false

      try {
        const data = await this.$store.dispatch('accountLevelSettings/getAccountSettings', this.accountId)
        this.settings = { ...data }
        this.originalSettings = { ...data }
      } catch (error) {
        this.isError = true
        this.$logger.handleError(error, `Failed to load settings for account ${this.accountId}`)
        this.$toaster.error('Failed to load account settings. Please try again.')
      } finally {
        this.isLoading = false
      }
    },
    startEditing () {
      this.isEditing = true
      this.originalSettings = { ...this.settings }
    },
    cancelEditing () {
      this.isEditing = false
      this.settings = { ...this.originalSettings }
    },
    async saveSettings () {
      this.isSaving = true

      try {
        // Only send MFA status for update
        const updateData = {
          isMfaEnabled: this.settings.isMfaEnabled
        }

        await this.$store.dispatch('accountLevelSettings/updateAccountSettings', {
          accountId: this.accountId,
          data: updateData
        })

        this.isEditing = false
        this.originalSettings = { ...this.settings }
        this.$toaster.success('MFA settings updated successfully!')
      } catch (error) {
        this.$logger.handleError(error, `Failed to update MFA settings for account ${this.accountId}`)
        this.$toaster.error('Failed to update MFA settings. Please try again.')
      } finally {
        this.isSaving = false
      }
    }
  }
}
</script>

<style scoped>
.card-header h5 {
  color: #495057;
}
</style>
